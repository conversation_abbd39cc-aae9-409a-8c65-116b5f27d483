// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
// Unit tests for p2psocket.h basic interface (excluding Stream API)

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#include <signal.h>
#endif

#include <mutex>
#include <string>
#include <vector>
#include <iostream>
#include <chrono>
#include <thread>
#include <cstring>
#include <cassert>
#include <atomic>
#include "p2psocket.h"
#include "cert_wrapper.h"

// Test logging levels
enum TEST_LOG_LEVEL {
    LOG_ERROR = 0,
    LOG_WARN = 1,
    LOG_INFO = 2,
    LOG_DEBUG = 3,
    LOG_VERBOSE = 4
};

// Simple logging class for tests
class TestLogger {
public:
    static void Log(const char* message) {
        std::cout << "[TEST] " << message << std::endl;
    }

    static void Log(int level, const std::string& message) {
        const char* levelStr[] = {"ERROR", "WARN", "INFO", "DEBUG", "VERBOSE"};
        std::cout << "[" << levelStr[level] << "] " << message << std::endl;
    }

    static void Log(const std::string& message) {
        std::cout << "[TEST] " << message << std::endl;
    }

    template<typename... Args>
    static void Log(int level, const char* format, Args... args) {
        char buffer[1024];
        snprintf(buffer, sizeof(buffer), format, args...);
        const char* levelStr[] = {"ERROR", "WARN", "INFO", "DEBUG", "VERBOSE"};
        std::cout << "[" << levelStr[level] << "] " << buffer << std::endl;
    }
};

// Test result tracking
struct TestResult {
    int total_tests = 0;
    int passed_tests = 0;
    int failed_tests = 0;
    std::vector<std::string> failures;
    
    void AddTest(const std::string& test_name, bool passed, const std::string& error = "") {
        total_tests++;
        if (passed) {
            passed_tests++;
            TestLogger::Log(LOG_INFO, "PASS: " + test_name);
        } else {
            failed_tests++;
            std::string failure_msg = "FAIL: " + test_name;
            if (!error.empty()) {
                failure_msg += " - " + error;
            }
            failures.push_back(failure_msg);
            TestLogger::Log(LOG_ERROR, failure_msg);
        }
    }
    
    void PrintSummary() {
        TestLogger::Log("=== Test Summary ===");
        TestLogger::Log("Total tests: " + std::to_string(total_tests));
        TestLogger::Log("Passed: " + std::to_string(passed_tests));
        TestLogger::Log("Failed: " + std::to_string(failed_tests));
        
        if (failed_tests > 0) {
            TestLogger::Log("Failures:");
            for (const auto& failure : failures) {
                TestLogger::Log("  " + failure);
            }
        }
    }
};

// Global test result tracker
TestResult g_testResult;

// Test assertion macros
#define TEST_ASSERT(condition, test_name) \
    do { \
        bool test_result = (condition); \
        g_testResult.AddTest(test_name, test_result, test_result ? "" : #condition); \
    } while(0)

#define TEST_ASSERT_MSG(condition, test_name, msg) \
    do { \
        bool test_result = (condition); \
        g_testResult.AddTest(test_name, test_result, test_result ? "" : msg); \
    } while(0)

// Utility functions
uint64_t GetCurrentTimeMs() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

static void SleepMs(int ms) {
#ifdef _WIN32
    Sleep(ms);
#else
    usleep(ms * 1000);
#endif
}

// Certificate verification callback for tests
static bool TestCertVerifyCallback(P2P_SOCKET soc, const char* x509) {
    TestLogger::Log(LOG_INFO, "Certificate verification callback called");
    return true; // Accept all certificates for testing
}

// Test configuration
struct TestConfig {
    enum SOCKET_TYPE socket_type = SOCKET_QUIC;
    int worker_num = 4;
    int test_port = 4433;
    std::string test_ip = "127.0.0.1";
    int buffer_size = 64 * 1024;
    int timeout_ms = 5000;

    // Test control flags
    bool run_basic_ops = true;
    bool run_invalid_params = true;
    bool run_binding = true;
    bool run_settings = true;
    bool run_polling = true;
    bool run_connection = true;
    bool run_vectored_io = true;
    bool run_multi_client = true;
    bool run_edge_cases = true;

    // Additional comprehensive test flags
    bool run_get_local_port = true;
    bool run_connection_timeout = true;
    bool run_data_size_boundaries = true;
    bool run_writev_advanced = true;
};

TestConfig g_config;

// Global variables for server-client testing
std::atomic<bool> g_server_running(false);
std::atomic<bool> g_client_connected(false);
std::atomic<bool> g_test_completed(false);
P2P_SOCKET g_server_socket = nullptr;
P2P_SOCKET g_accepted_socket = nullptr;
std::mutex g_test_mutex;

// Global variables for multi-client testing
std::atomic<int> g_connected_clients(0);
std::atomic<int> g_expected_clients(0);
std::vector<P2P_SOCKET> g_client_sockets;
std::mutex g_client_sockets_mutex;

// Helper function to create socket options
SocketOptions CreateSocketOptions(enum SOCKET_MODE mode, CertHandle cert_handle) {
    SocketOptions options = {};
    options.mode = mode;
    options.type = g_config.socket_type;
    options.wokernum = g_config.worker_num;
    options.cert_verify = TestCertVerifyCallback;
    options.cert = Cert_GetX509Str(cert_handle);
    options.privatekey = Cert_GetPkeyStr(cert_handle);
    options.log_level = P2P_LOG_INFO;
    return options;
}

// Server thread function for connection testing
void ServerThreadFunction(CertHandle cert_handle, int test_port,int message_len) {
    TestLogger::Log("Server thread starting..." + std::to_string(message_len));

    SocketOptions server_options = CreateSocketOptions(MODE_SERVER, cert_handle);
    g_server_socket = P2pCreate(&server_options);

    if (!g_server_socket) {
        TestLogger::Log(LOG_ERROR, "Failed to create server socket");
        return;
    }

    // Bind to port - P2pBind only saves parameters, should always succeed
    P2pBind(g_server_socket, "0.0.0.0", test_port); // Should always succeed

    // Listen for connections
    int result = P2pListen(g_server_socket);
    if (result != 0) {
        TestLogger::Log(LOG_ERROR, "Failed to listen on server socket");
        P2pClose(g_server_socket);
        return;
    }

    g_server_running = true;
    TestLogger::Log("Server listening on port " + std::to_string(test_port));

    // Accept connection
    char client_ip[128] = {0};
    int client_port = 0;
    g_accepted_socket = P2pAccept(g_server_socket, client_ip, sizeof(client_ip), &client_port);

    if (g_accepted_socket) {
        TestLogger::Log("Server accepted connection from " + std::string(client_ip) + ":" + std::to_string(client_port));

        // Simple echo server - read data and echo it back
        char* buffer = new char[message_len];
        while (!g_test_completed) {
            int bytes_read = P2pRead(g_accepted_socket, buffer, message_len);
            if (bytes_read > 0) {
                TestLogger::Log("Server received " + std::to_string(bytes_read) + " bytes");
                int bytes_written = P2pWrite(g_accepted_socket, buffer, bytes_read);
                TestLogger::Log("Server echoed " + std::to_string(bytes_written) + " bytes");
            } else if (bytes_read < 0) {
                TestLogger::Log("Server read error or connection closed");
                break;
            }
            SleepMs(10); // Small delay to prevent busy waiting
        }

        P2pClose(g_accepted_socket);
        delete[] buffer;
        g_accepted_socket = nullptr;
    }

    P2pClose(g_server_socket);
    g_server_socket = nullptr;
    g_server_running = false;
    TestLogger::Log("Server thread exiting...");
}

// Handle individual client connection for multi-client test
void HandleClientConnection(P2P_SOCKET client_socket, int client_id) {
    TestLogger::Log("Client handler " + std::to_string(client_id) + " started");

    char buffer[1024];
    int message_count = 0;

    while (!g_test_completed && message_count < 3) { // Each client sends 3 messages
        // Calculate expected message length: "Message" + msg_num + "FromClient" + client_id
        // For example: "Message0FromClient0", "Message1FromClient1", etc.
        std::string expected_message = "Message" + std::to_string(message_count) + "FromClient" + std::to_string(client_id);
        int expected_length = expected_message.length();

        int bytes_read = P2pRead(client_socket, buffer, expected_length);
        if (bytes_read > 0) {
            buffer[bytes_read] = '\0'; // Null terminate for string operations
            TestLogger::Log("Client handler " + std::to_string(client_id) + " received: " + std::string(buffer));

            // Echo back with client ID prefix
            std::string response = "Echo-Client" + std::to_string(client_id) + ":" + std::string(buffer);
            int bytes_written = P2pWrite(client_socket, response.c_str(), response.length());

            if (bytes_written > 0) {
                TestLogger::Log("Client handler " + std::to_string(client_id) + " echoed: " + response);
                message_count++;
            }
        } else if (bytes_read < 0) {
            TestLogger::Log("Client handler " + std::to_string(client_id) + " read error or connection closed");
            break;
        }
        SleepMs(10); // Small delay
    }

    P2pClose(client_socket);
    TestLogger::Log("Client handler " + std::to_string(client_id) + " exiting");
}

// Multi-client server function
void MultiClientServerFunction(CertHandle cert_handle, int test_port) {
    TestLogger::Log("Multi-client server thread starting...");

    SocketOptions server_options = CreateSocketOptions(MODE_SERVER, cert_handle);
    g_server_socket = P2pCreate(&server_options);

    if (!g_server_socket) {
        TestLogger::Log(LOG_ERROR, "Failed to create multi-client server socket");
        return;
    }

    // Bind to port - P2pBind only saves parameters, should always succeed
    P2pBind(g_server_socket, "0.0.0.0", test_port); // Should always succeed

    // Listen for connections
    int result = P2pListen(g_server_socket);
    if (result != 0) {
        TestLogger::Log(LOG_ERROR, "Failed to listen on multi-client server socket");
        P2pClose(g_server_socket);
        return;
    }

    g_server_running = true;
    TestLogger::Log("Multi-client server listening on port " + std::to_string(test_port));

    std::vector<std::thread> client_handlers;
    int accepted_count = 0;

    // Accept multiple client connections
    while (accepted_count < g_expected_clients && !g_test_completed) {
        char client_ip[128] = {0};
        int client_port = 0;
        P2P_SOCKET client_socket = P2pAccept(g_server_socket, client_ip, sizeof(client_ip), &client_port);

        if (client_socket) {
            TestLogger::Log("Multi-client server accepted connection " + std::to_string(accepted_count) +
                            " from " + std::string(client_ip) + ":" + std::to_string(client_port));

            {
                std::lock_guard<std::mutex> lock(g_client_sockets_mutex);
                g_client_sockets.push_back(client_socket);
            }

            // Create handler thread for this client
            client_handlers.emplace_back([client_socket, accepted_count]() {
                HandleClientConnection(client_socket, accepted_count);
            });
            accepted_count++;
            g_connected_clients++;
        }
        // Set a timeout for accept to avoid infinite blocking
        // PollEvent poll_event;
        // poll_event.events = P2PPOLLIN;
        // int poll_result = P2pPoll(g_server_socket, &poll_event, 2000); // 2 second timeout

        // if (poll_result > 0 && (poll_event.revents & P2PPOLLIN)) {

        // } else if (poll_result == 0) {
        //     TestLogger::Log("Multi-client server accept timeout");
        // }
    }

    TestLogger::Log("Multi-client server accepted " + std::to_string(accepted_count) + " clients");

    // Wait for all client handlers to complete
    for (auto& handler : client_handlers) {
        if (handler.joinable()) {
            handler.join();
        }
    }

    P2pClose(g_server_socket);
    g_server_socket = nullptr;
    g_server_running = false;
    TestLogger::Log("Multi-client server thread exiting...");
}

// Client connection function for multi-client test
void ClientConnectionFunction(CertHandle cert_handle, int test_port, int client_id, bool* result) {
    TestLogger::Log("Client " + std::to_string(client_id) + " thread starting...");

    *result = false; // Initialize result

    // Create client socket
    SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET client_socket = P2pCreate(&client_options);

    if (!client_socket) {
        TestLogger::Log(LOG_ERROR, "Client " + std::to_string(client_id) + " failed to create socket");
        return;
    }

    // Set connection timeout
    int timeout_result = P2pSetConnTimeout(client_socket, 5000); // 5 seconds
    if (timeout_result != 0) {
        TestLogger::Log(LOG_ERROR, "Client " + std::to_string(client_id) + " failed to set timeout");
        P2pClose(client_socket);
        return;
    }

    // Connect to server
    int connect_result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port);
    if (connect_result != 0) {
        TestLogger::Log(LOG_ERROR, "Client " + std::to_string(client_id) + " failed to connect");
        P2pClose(client_socket);
        return;
    }

    TestLogger::Log("Client " + std::to_string(client_id) + " connected successfully");

    // Send multiple messages and verify echoes
    bool all_messages_ok = true;
    for (int msg = 0; msg < 3; msg++) {
        std::string message = "Message" + std::to_string(msg) + "FromClient" + std::to_string(client_id);

        // Send message
        int bytes_written = P2pWrite(client_socket, message.c_str(), message.length());
        if (bytes_written != (int)message.length()) {
            TestLogger::Log(LOG_ERROR, "Client " + std::to_string(client_id) + " failed to send message " + std::to_string(msg));
            all_messages_ok = false;
            break;
        }

        TestLogger::Log("Client " + std::to_string(client_id) + " sent: " + message);

        // Read echo response - calculate expected echo length
        std::string expected_prefix = "Echo-Client" + std::to_string(client_id) + ":" + message;
        int expected_echo_length = expected_prefix.length();

        char read_buffer[1024] = {0};
        int bytes_read = P2pRead(client_socket, read_buffer, expected_echo_length);
        if (bytes_read <= 0) {
            TestLogger::Log(LOG_ERROR, "Client " + std::to_string(client_id) + " failed to read echo for message " + std::to_string(msg));
            all_messages_ok = false;
            break;
        }

        std::string received(read_buffer, bytes_read);

        if (received == expected_prefix) {
            TestLogger::Log("Client " + std::to_string(client_id) + " received correct echo: " + received);
        } else {
            TestLogger::Log(LOG_ERROR, "Client " + std::to_string(client_id) + " received incorrect echo. Expected: " +
                          expected_prefix + ", Got: " + received);
            all_messages_ok = false;
            break;
        }

        SleepMs(100); // Small delay between messages
    }

    P2pClose(client_socket);
    *result = all_messages_ok;
    TestLogger::Log("Client " + std::to_string(client_id) + " thread exiting with result: " +
                   (all_messages_ok ? "SUCCESS" : "FAILURE"));
}

// Basic socket creation and destruction tests
void TestBasicSocketOperations() {
    TestLogger::Log("=== Testing Basic Socket Operations ===");

    CertHandle cert_handle = Cert_Create();
    TEST_ASSERT(cert_handle != nullptr, "Certificate creation");

    if (cert_handle) {
        // Test socket creation
        SocketOptions options = CreateSocketOptions(MODE_SERVER, cert_handle);
        P2P_SOCKET socket = P2pCreate(&options);
        TEST_ASSERT(socket != nullptr, "Socket creation");

        if (socket) {
            // Test socket close
            int close_result = P2pClose(socket);
            TEST_ASSERT(close_result == 0, "Socket close");
        }

        Cert_Destroy(cert_handle);
    }
}

// Test invalid parameter handling
void TestInvalidParameters() {
    TestLogger::Log("=== Testing Invalid Parameters ===");
    
    // Test null parameters
    P2P_SOCKET socket = P2pCreate(nullptr);
    TEST_ASSERT(socket == nullptr, "Create with null options");
    
    int result = P2pClose(nullptr);
    TEST_ASSERT(result != 0, "Close null socket");
    
    result = P2pBind(nullptr, "127.0.0.1", 4433);
    TEST_ASSERT(result != 0, "Bind null socket");
    
    result = P2pConnect(nullptr, "127.0.0.1", 4433);
    TEST_ASSERT(result != 0, "Connect null socket");
    
    result = P2pWrite(nullptr, "test", 4);
    TEST_ASSERT(result <= 0, "Write to null socket");
    
    char buffer[100];
    result = P2pRead(nullptr, buffer, sizeof(buffer));
    TEST_ASSERT(result <= 0, "Read from null socket");
}

// Test socket binding and port operations
void TestSocketBinding() {
    TestLogger::Log("=== Testing Socket Binding ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for binding test");
        return;
    }

    SocketOptions options = CreateSocketOptions(MODE_SERVER, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test binding to specific port - P2pBind only saves parameters
        int result = P2pBind(socket, "0.0.0.0", g_config.test_port);
        TEST_ASSERT(result == 0, "Bind to specific port (saves parameters)");

        // Test getting local port before listen - may not return correct value
        int local_port_before = P2pGetLocalPort(socket);
        TestLogger::Log("Local port before listen: " + std::to_string(local_port_before));

        // Test listen - this actually binds to the port
        result = P2pListen(socket);
        TEST_ASSERT(result == 0, "Listen on bound socket (actual binding)");

        // Test getting local port after listen - should return correct value
        int local_port_after = P2pGetLocalPort(socket);
        TEST_ASSERT(local_port_after == g_config.test_port, "Get local port after listen");
        TestLogger::Log("Local port after listen: " + std::to_string(local_port_after));

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for binding test");
    }

    Cert_Destroy(cert_handle);
}

// Test read/write modes behavior with actual data transmission
void TestReadWriteModes() {
    TestLogger::Log("=== Testing Read/Write Modes Behavior ===");

    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for read/write modes test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    int test_port = g_config.test_port + 50; // Use different port to avoid conflicts

    // Test different combinations of send/read modes
    struct ModeTest {
        int send_mode;
        int read_mode;
        const char* description;
    };

    ModeTest mode_tests[] = {
        {0, 0, "Buffer Send + Buffer Read"},
        {0, 1, "Buffer Send + Direct Read"},
        {1, 0, "Direct Send + Buffer Read"},
        {1, 1, "Direct Send + Direct Read"}
    };

    for (int test_idx = 0; test_idx < 4; test_idx++) {
        ModeTest& test = mode_tests[test_idx];
        TestLogger::Log("Testing mode combination: " + std::string(test.description));

        // Reset global state
        g_server_running = false;
        g_client_connected = false;
        g_test_completed = false;

        // Start server thread for this mode test
        std::thread server_thread([server_cert, test_port, test_idx, &test]() {
            TestLogger::Log("Mode test server thread starting for: " + std::string(test.description));

            SocketOptions server_options = CreateSocketOptions(MODE_SERVER, server_cert);
            P2P_SOCKET server_socket = P2pCreate(&server_options);

            if (!server_socket) {
                TestLogger::Log(LOG_ERROR, "Failed to create mode test server socket");
                return;
            }

            // Bind and listen - P2pBind only saves parameters, should always succeed
            P2pBind(server_socket, "0.0.0.0", test_port + test_idx); // Should always succeed

            if (P2pListen(server_socket) != 0) {
                TestLogger::Log(LOG_ERROR, "Failed to listen on mode test server socket");
                P2pClose(server_socket);
                return;
            }

            g_server_running = true;
            TestLogger::Log("Mode test server listening on port " + std::to_string(test_port + test_idx));

            // Accept client connection
            char client_ip[128] = {0};
            int client_port = 0;

            P2P_SOCKET client_socket = P2pAccept(server_socket, client_ip, sizeof(client_ip), &client_port);

            if (client_socket) {
                TestLogger::Log("Mode test server accepted connection for: " + std::string(test.description));

                // Set server read mode (opposite of client for testing)
                P2pSetReadMode(client_socket, test.read_mode);
                P2pSetSendMode(client_socket, test.send_mode);

                // Echo test data
                const char* test_message = "Mode test message";
                int expected_len = strlen(test_message);

                char buffer[256];
                int bytes_read = P2pRead(client_socket, buffer, expected_len);
                if (bytes_read > 0) {
                    buffer[bytes_read] = '\0';
                    TestLogger::Log("Mode test server received: " + std::string(buffer));

                    // Echo back with mode info
                    std::string response = "Echo[" + std::string(test.description) + "]: " + std::string(buffer);
                    int bytes_written = P2pWrite(client_socket, response.c_str(), response.length());
                    TestLogger::Log("Mode test server echoed " + std::to_string(bytes_written) + " bytes");
                }

                P2pClose(client_socket);
            }

            P2pClose(server_socket);
            g_server_running = false;
            TestLogger::Log("Mode test server thread exiting for: " + std::string(test.description));
        });

        // Wait for server to start
        int wait_count = 0;
        while (!g_server_running && wait_count < 50) {
            SleepMs(100);
            wait_count++;
        }

        if (g_server_running) {
            // Create client socket and test the specific mode combination
            SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, client_cert);
            P2P_SOCKET client_socket = P2pCreate(&client_options);

            if (client_socket) {
                // Set client modes
                int send_result = P2pSetSendMode(client_socket, test.send_mode);
                int read_result = P2pSetReadMode(client_socket, test.read_mode);

                TEST_ASSERT(send_result == 0, "Set client send mode for " + std::string(test.description));
                TEST_ASSERT(read_result == 0, "Set client read mode for " + std::string(test.description));

                P2pSetConnTimeout(client_socket, 5000);

                // Connect to server
                int connect_result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port + test_idx);
                TEST_ASSERT(connect_result == 0, "Client connect for " + std::string(test.description));

                if (connect_result == 0) {
                    TestLogger::Log("Mode test client connected for: " + std::string(test.description));

                    // Send test message
                    const char* test_message = "Mode test message";
                    auto start_time = std::chrono::high_resolution_clock::now();

                    int bytes_written = P2pWrite(client_socket, test_message, strlen(test_message));

                    auto write_time = std::chrono::high_resolution_clock::now();
                    auto write_duration = std::chrono::duration_cast<std::chrono::microseconds>(write_time - start_time);

                    TEST_ASSERT(bytes_written == (int)strlen(test_message), "Client write for " + std::string(test.description));
                    TestLogger::Log("Write completed in " + std::to_string(write_duration.count()) + " microseconds for " + std::string(test.description));

                    if (bytes_written > 0) {
                        // Read echo response
                        std::string expected_echo = "Echo[" + std::string(test.description) + "]: " + std::string(test_message);
                        int expected_echo_len = expected_echo.length();

                        char read_buffer[256] = {0};
                        auto read_start = std::chrono::high_resolution_clock::now();

                        int bytes_read = P2pRead(client_socket, read_buffer, expected_echo_len);

                        auto read_end = std::chrono::high_resolution_clock::now();
                        auto read_duration = std::chrono::duration_cast<std::chrono::microseconds>(read_end - read_start);

                        TEST_ASSERT(bytes_read == expected_echo_len, "Client read for " + std::string(test.description));
                        TEST_ASSERT(strcmp(read_buffer, expected_echo.c_str()) == 0, "Echo data matches for " + std::string(test.description));

                        TestLogger::Log("Read completed in " + std::to_string(read_duration.count()) + " microseconds for " + std::string(test.description));
                        TestLogger::Log("Mode test successful for: " + std::string(test.description));
                    }
                }

                P2pClose(client_socket);
            }
        }

        // Signal test completion and wait for server thread
        g_test_completed = true;
        if (server_thread.joinable()) {
            server_thread.join();
        }

        SleepMs(200); // Small delay between mode tests
    }

    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);
    TestLogger::Log("Read/Write modes behavior test completed");
}

// Test timeout and mode settings with actual behavior verification
void TestSocketSettings() {
    TestLogger::Log("=== Testing Socket Settings ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for settings test");
        return;
    }

    // Test 1: Basic API functionality
    TestLogger::Log("Testing basic settings API functionality");
    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test connection timeout setting
        int result = P2pSetConnTimeout(socket, g_config.timeout_ms);
        TEST_ASSERT(result == 0, "Set connection timeout");

        // Test send mode setting - both modes
        result = P2pSetSendMode(socket, 1); // Direct mode
        TEST_ASSERT(result == 0, "Set send mode to direct");

        result = P2pSetSendMode(socket, 0); // Buffer mode
        TEST_ASSERT(result == 0, "Set send mode to buffer");

        // Test read mode setting - both modes
        result = P2pSetReadMode(socket, 1); // Direct mode
        TEST_ASSERT(result == 0, "Set read mode to direct");

        result = P2pSetReadMode(socket, 0); // Buffer mode
        TEST_ASSERT(result == 0, "Set read mode to buffer");

        // Test invalid mode values
        result = P2pSetSendMode(socket, 2); // Invalid mode
        TEST_ASSERT(result != 0, "Reject invalid send mode");

        result = P2pSetReadMode(socket, -1); // Invalid mode
        TEST_ASSERT(result != 0, "Reject invalid read mode");

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for settings test");
    }

    // Test 2: Read/Write modes behavior testing
    TestLogger::Log("Testing read/write modes behavior with actual connections");
    TestReadWriteModes();

    Cert_Destroy(cert_handle);
}
// Test polling operations with comprehensive read/write event testing
void TestPollingOperations() {
    TestLogger::Log("=== Testing Polling Operations ===");

    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for polling test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    int test_port = g_config.test_port + 40; // Use different port to avoid conflicts

    // Test 1: Basic polling on unconnected socket (timeout test)
    TestLogger::Log("Testing basic polling timeout on unconnected socket");
    SocketOptions options = CreateSocketOptions(MODE_CLIENT, client_cert);
    P2P_SOCKET client_socket = P2pCreate(&options);

    if (client_socket) {
        PollEvent events;
        events.events = P2PPOLLIN | P2PPOLLOUT;
        events.revents = 0;

        int result = P2pPoll(client_socket, &events, 100); // 100ms timeout
        TEST_ASSERT(result >= 0, "Basic socket poll operation");
        TestLogger::Log("Basic poll result: " + std::to_string(result) + ", revents: " + std::to_string(events.revents));

        P2pClose(client_socket);
    } else {
        TEST_ASSERT(false, "Socket creation for basic polling test");
    }

    // Test 2: Polling with server-client connection for read/write events
    TestLogger::Log("Testing polling with server-client connection");

    // Reset global state
    g_server_running = false;
    g_client_connected = false;
    g_test_completed = false;

    // Start server thread for polling test
    std::thread server_thread([server_cert, test_port]() {
        TestLogger::Log("Polling test server thread starting...");

        SocketOptions server_options = CreateSocketOptions(MODE_SERVER, server_cert);
        P2P_SOCKET server_socket = P2pCreate(&server_options);

        if (!server_socket) {
            TestLogger::Log(LOG_ERROR, "Failed to create polling test server socket");
            return;
        }

        // Bind and listen - P2pBind only saves parameters, should always succeed
        P2pBind(server_socket, "0.0.0.0", test_port); // Should always succeed

        if (P2pListen(server_socket) != 0) {
            TestLogger::Log(LOG_ERROR, "Failed to listen on polling test server socket");
            P2pClose(server_socket);
            return;
        }

        g_server_running = true;
        TestLogger::Log("Polling test server listening on port " + std::to_string(test_port));

        // Accept one client connection
        char client_ip[128] = {0};
        int client_port = 0;

        // Poll for incoming connection
        PollEvent poll_event;
        poll_event.events = P2PPOLLIN;
        poll_event.revents = 0;

        int poll_result = P2pPoll(server_socket, &poll_event, 5000); // 5 second timeout
        if (poll_result > 0 && (poll_event.revents & P2PPOLLIN)) {
            P2P_SOCKET client_socket = P2pAccept(server_socket, client_ip, sizeof(client_ip), &client_port);

            if (client_socket) {
                TestLogger::Log("Polling test server accepted connection from " + std::string(client_ip) + ":" + std::to_string(client_port));

                // Test polling for read events
                char buffer[256];
                while (!g_test_completed) {
                    PollEvent read_event;
                    read_event.events = P2PPOLLIN;
                    read_event.revents = 0;

                    int read_poll = P2pPoll(client_socket, &read_event, 1000); // 1 second timeout
                    if (read_poll > 0 && (read_event.revents & P2PPOLLIN)) {
                        int bytes_read = P2pRead(client_socket, buffer, sizeof(buffer) - 1);
                        if (bytes_read > 0) {
                            buffer[bytes_read] = '\0';
                            TestLogger::Log("Polling test server received via poll: " + std::string(buffer));

                            // Echo back the data
                            std::string response = "Echo: " + std::string(buffer);
                            P2pWrite(client_socket, response.c_str(), response.length());
                            TestLogger::Log("Polling test server echoed: " + response);
                        } else if (bytes_read < 0) {
                            TestLogger::Log("Polling test server read error or connection closed");
                            break;
                        }
                    } else if (read_poll == 0) {
                        // Timeout - continue polling
                        continue;
                    } else {
                        TestLogger::Log("Polling test server poll error");
                        break;
                    }
                }

                P2pClose(client_socket);
            }
        }

        P2pClose(server_socket);
        g_server_running = false;
        TestLogger::Log("Polling test server thread exiting...");
    });

    // Wait for server to start
    int wait_count = 0;
    while (!g_server_running && wait_count < 50) {
        SleepMs(100);
        wait_count++;
    }

    TEST_ASSERT(g_server_running, "Polling test server started successfully");

    if (g_server_running) {
        // Create client socket and test polling
        SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, client_cert);
        client_socket = P2pCreate(&client_options);
        TEST_ASSERT(client_socket != nullptr, "Polling test client socket creation");

        if (client_socket) {
            P2pSetConnTimeout(client_socket, 5000);

            // Test POLLOUT before connection (should be available)
            PollEvent write_event;
            write_event.events = P2PPOLLOUT;
            write_event.revents = 0;

            int write_poll = P2pPoll(client_socket, &write_event, 1000);
            TestLogger::Log("Pre-connection POLLOUT result: " + std::to_string(write_poll) + ", revents: " + std::to_string(write_event.revents));

            // Connect to server
            int connect_result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port);
            TEST_ASSERT(connect_result == 0, "Polling test client connect to server");

            if (connect_result == 0) {
                TestLogger::Log("Polling test client connected successfully");

                // Test POLLOUT after connection (should be available for writing)
                write_event.events = P2PPOLLOUT;
                write_event.revents = 0;

                write_poll = P2pPoll(client_socket, &write_event, 1000);
                TEST_ASSERT(write_poll > 0, "POLLOUT event detection after connection");
                TEST_ASSERT(write_event.revents & P2PPOLLOUT, "POLLOUT flag set in revents");
                TestLogger::Log("Post-connection POLLOUT result: " + std::to_string(write_poll) + ", revents: " + std::to_string(write_event.revents));

                // Send data and test polling
                const char* test_message = "Hello from polling test client!";
                int bytes_written = P2pWrite(client_socket, test_message, strlen(test_message));
                TEST_ASSERT(bytes_written == (int)strlen(test_message), "Polling test client write data");

                if (bytes_written > 0) {
                    TestLogger::Log("Polling test client sent: " + std::string(test_message));

                    // Poll for read event (echo response)
                    PollEvent read_event;
                    read_event.events = P2PPOLLIN;
                    read_event.revents = 0;

                    int read_poll = P2pPoll(client_socket, &read_event, 3000); // 3 second timeout
                    TEST_ASSERT(read_poll > 0, "POLLIN event detection for echo response");
                    TEST_ASSERT(read_event.revents & P2PPOLLIN, "POLLIN flag set in revents");
                    TestLogger::Log("Echo POLLIN result: " + std::to_string(read_poll) + ", revents: " + std::to_string(read_event.revents));

                    if (read_poll > 0 && (read_event.revents & P2PPOLLIN)) {
                        // Read the echo response
                        char read_buffer[256] = {0};
                        int expected_echo_len = strlen("Echo: ") + strlen(test_message);
                        int bytes_read = P2pRead(client_socket, read_buffer, expected_echo_len);
                        TEST_ASSERT(bytes_read == expected_echo_len, "Polling test client read echoed data");

                        std::string expected_echo = "Echo: " + std::string(test_message);
                        TEST_ASSERT(strcmp(read_buffer, expected_echo.c_str()) == 0, "Polling test echoed data matches");
                        TestLogger::Log("Polling test client received: " + std::string(read_buffer));
                    }
                }

                // Test combined events (POLLIN | POLLOUT)
                PollEvent combined_event;
                combined_event.events = P2PPOLLIN | P2PPOLLOUT;
                combined_event.revents = 0;

                int combined_poll = P2pPoll(client_socket, &combined_event, 1000);
                TestLogger::Log("Combined events poll result: " + std::to_string(combined_poll) + ", revents: " + std::to_string(combined_event.revents));
                TEST_ASSERT(combined_poll >= 0, "Combined events polling");
            }

            P2pClose(client_socket);
        }
    }

    // Signal test completion and wait for server thread
    g_test_completed = true;
    if (server_thread.joinable()) {
        server_thread.join();
    }

    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);
    TestLogger::Log("Polling operations test completed");
}

// Test real server-client connection and data transmission
void TestServerClientConnection() {
    TestLogger::Log("=== Testing Server-Client Connection and Data Transmission ===");

    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for connection test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    int test_port = g_config.test_port + 10; // Use different port to avoid conflicts

    // Reset global state
    g_server_running = false;
    g_client_connected = false;
    g_test_completed = false;
    // Test data transmission
    const char* test_message = "Hello from client!";
    int message_len = strlen(test_message);
    // Start server thread
    std::thread server_thread([server_cert, test_port,message_len]() {
        ServerThreadFunction(server_cert, test_port,message_len);
    });

    // Wait for server to start
    int wait_count = 0;
    while (!g_server_running && wait_count < 50) { // Wait up to 5 seconds
        SleepMs(100);
        wait_count++;
    }

    TEST_ASSERT(g_server_running, "Server started successfully");

    if (g_server_running) {
        // Create client socket
        SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, client_cert);
        P2P_SOCKET client_socket = P2pCreate(&client_options);
        TEST_ASSERT(client_socket != nullptr, "Client socket creation");

        if (client_socket) {
            // Set connection timeout
            int result = P2pSetConnTimeout(client_socket, 5000); // 5 seconds
            TEST_ASSERT(result == 0, "Set client connection timeout");

            // Connect to server
            result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port);
            TEST_ASSERT(result == 0, "Client connect to server");

            if (result == 0) {
                g_client_connected = true;
                TestLogger::Log("Client connected successfully");


                // Test P2pWrite
                int bytes_written = P2pWrite(client_socket, test_message, message_len);
                TEST_ASSERT(bytes_written == message_len, "Client write data");

                if (bytes_written > 0) {
                    // Test P2pRead - read echo from server
                    char read_buffer[1024] = {0};
                    int bytes_read = P2pRead(client_socket, read_buffer, message_len);
                    TEST_ASSERT(bytes_read == message_len, "Client read echoed data");
                    TEST_ASSERT(strcmp(read_buffer, test_message) == 0, "Echoed data matches sent data");

                    TestLogger::Log("Data echo test successful");
                }
            }

            P2pClose(client_socket);
        }
    }

    // Signal test completion and wait for server thread
    g_test_completed = true;
    if (server_thread.joinable()) {
        server_thread.join();
    }

    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);
}

// Test vectored I/O operations with real connection
void TestVectoredIO() {
    TestLogger::Log("=== Testing Vectored I/O Operations ===");

    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for vectored IO test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    int test_port = g_config.test_port + 20; // Use different port

    // Reset global state
    g_server_running = false;
    g_client_connected = false;
    g_test_completed = false;
    struct p2p_iovec iov[3];
    char data1[] = "Hello";
    char data2[] = " ";
    char data3[] = "World!";
    int total_len = strlen(data1) + strlen(data2) + strlen(data3);

    iov[0].iov_base = data1;
    iov[0].iov_len = strlen(data1);
    iov[1].iov_base = data2;
    iov[1].iov_len = strlen(data2);
    iov[2].iov_base = data3;
    iov[2].iov_len = strlen(data3);
    // Start server thread
    std::thread server_thread([server_cert, test_port, total_len]() {
        ServerThreadFunction(server_cert, test_port,total_len);
    });

    // Wait for server to start
    int wait_count = 0;
    while (!g_server_running && wait_count < 50) {
        SleepMs(100);
        wait_count++;
    }

    if (g_server_running) {
        // Create client socket
        SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, client_cert);
        P2P_SOCKET client_socket = P2pCreate(&client_options);

        if (client_socket) {
            P2pSetConnTimeout(client_socket, 5000);
            int result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port);

            if (result == 0) {
                // Test P2pWritev with multiple buffers

                int bytes_written = P2pWritev(client_socket, iov, 3);
                TEST_ASSERT(bytes_written == total_len, "Vectored write operation");

                if (bytes_written > 0) {
                    // Read the echoed data
                    char read_buffer[1024] = {0};
                    int bytes_read = P2pRead(client_socket, read_buffer, total_len);
                    TEST_ASSERT(bytes_read == total_len, "Read echoed vectored data");
                    TEST_ASSERT(strcmp(read_buffer, "Hello World!") == 0, "Vectored data integrity");

                    TestLogger::Log("Vectored I/O test successful");
                }
            } else {
                TEST_ASSERT(false, "Client connection failed for vectored IO test");
            }

            P2pClose(client_socket);
        }
    }

    // Test with null parameters
    P2P_SOCKET dummy_socket = nullptr;
    int result = P2pWritev(dummy_socket, nullptr, 0);
    TEST_ASSERT(result <= 0, "Writev with null socket");

    // Signal test completion and wait for server thread
    g_test_completed = true;
    if (server_thread.joinable()) {
        server_thread.join();
    }

    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);
}

// Test P2pGetLocalPort functionality
void TestGetLocalPort() {
    TestLogger::Log("=== Testing P2pGetLocalPort ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for GetLocalPort test");
        return;
    }

    // Test 1: Unbound socket should return error
    TestLogger::Log("Testing GetLocalPort on unbound socket");
    SocketOptions options = CreateSocketOptions(MODE_SERVER, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        int port_before = P2pGetLocalPort(socket);
        TEST_ASSERT(port_before < 0, "GetLocalPort on unbound socket should return error");
        TestLogger::Log("Unbound socket port result: " + std::to_string(port_before));
        P2pClose(socket);
    }

    // Test 2: Auto-assigned port (port 0) - need to call P2pListen for actual port assignment
    TestLogger::Log("Testing auto-assigned port (port 0)");
    socket = P2pCreate(&options);
    if (socket) {
        int bind_result = P2pBind(socket, "0.0.0.0", 0);
        TEST_ASSERT(bind_result == 0, "Bind to port 0 for auto-assignment");

        if (bind_result == 0) {
            // P2pGetLocalPort before listen might not return the actual port
            int port_before_listen = P2pGetLocalPort(socket);
            TestLogger::Log("Port before listen: " + std::to_string(port_before_listen));

            // Call P2pListen to actually assign the port
            int listen_result = P2pListen(socket);
            TEST_ASSERT(listen_result == 0, "Listen for auto-port assignment");

            if (listen_result == 0) {
                int auto_port = P2pGetLocalPort(socket);
                TEST_ASSERT(auto_port > 0, "Auto-assigned port should be positive");
                TEST_ASSERT(auto_port <= 65535, "Auto-assigned port should be valid");
                TestLogger::Log("Auto-assigned port after listen: " + std::to_string(auto_port));
            }
        }
        P2pClose(socket);
    }

    // Test 3: Specific port binding - need P2pListen for actual binding
    TestLogger::Log("Testing specific port binding");
    socket = P2pCreate(&options);
    if (socket) {
        int test_port = g_config.test_port + 100;
        int bind_result = P2pBind(socket, "0.0.0.0", test_port);
        TEST_ASSERT(bind_result == 0, "Bind to specific port");

        if (bind_result == 0) {
            // P2pGetLocalPort before listen might not return the actual port
            int port_before_listen = P2pGetLocalPort(socket);
            TestLogger::Log("Port before listen: " + std::to_string(port_before_listen));

            // Call P2pListen to actually bind the port
            int listen_result = P2pListen(socket);
            TEST_ASSERT(listen_result == 0, "Listen on specific port");

            if (listen_result == 0) {
                int bound_port = P2pGetLocalPort(socket);
                TEST_ASSERT(bound_port == test_port, "GetLocalPort should return bound port after listen");
                TestLogger::Log("Bound port after listen: " + std::to_string(bound_port) + ", expected: " + std::to_string(test_port));
            }
        }
        P2pClose(socket);
    }

    // Test 4: Different IP addresses - need P2pListen for actual binding
    TestLogger::Log("Testing different IP addresses");
    socket = P2pCreate(&options);
    if (socket) {
        int test_port = g_config.test_port + 101;
        int bind_result = P2pBind(socket, "127.0.0.1", test_port);
        TEST_ASSERT(bind_result == 0, "Bind to localhost");

        if (bind_result == 0) {
            // Call P2pListen to actually bind to the address
            int listen_result = P2pListen(socket);
            TEST_ASSERT(listen_result == 0, "Listen on localhost");

            if (listen_result == 0) {
                int bound_port = P2pGetLocalPort(socket);
                TEST_ASSERT(bound_port == test_port, "GetLocalPort should work with localhost binding after listen");
                TestLogger::Log("Localhost bound port after listen: " + std::to_string(bound_port));
            }
        }
        P2pClose(socket);
    }

    // Test 5: After listen
    TestLogger::Log("Testing GetLocalPort after listen");
    socket = P2pCreate(&options);
    if (socket) {
        int test_port = g_config.test_port + 102;
        P2pBind(socket, "0.0.0.0", test_port);
        int listen_result = P2pListen(socket);
        TEST_ASSERT(listen_result == 0, "Listen on bound socket");

        if (listen_result == 0) {
            int listening_port = P2pGetLocalPort(socket);
            TEST_ASSERT(listening_port == test_port, "GetLocalPort should work after listen");
            TestLogger::Log("Listening port: " + std::to_string(listening_port));
        }
        P2pClose(socket);
    }

    // Test 6: Invalid socket handle
    TestLogger::Log("Testing GetLocalPort with invalid socket");
    int invalid_result = P2pGetLocalPort(nullptr);
    TEST_ASSERT(invalid_result < 0, "GetLocalPort with null socket should return error");

    Cert_Destroy(cert_handle);
    TestLogger::Log("GetLocalPort test completed");
}

// Test connection timeout functionality
void TestConnectionTimeout() {
    TestLogger::Log("=== Testing Connection Timeout ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for timeout test");
        return;
    }

    // Test 1: Short timeout with unreachable address
    TestLogger::Log("Testing short timeout with unreachable address");
    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Set short timeout
        int timeout_result = P2pSetConnTimeout(socket, 1000); // 1 second
        TEST_ASSERT(timeout_result == 0, "Set connection timeout to 1 second");

        auto start_time = std::chrono::high_resolution_clock::now();

        // Try to connect to unreachable address (RFC5737 test address)
        int connect_result = P2pConnect(socket, "*********", 12345);

        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        TEST_ASSERT(connect_result != 0, "Connection to unreachable address should fail");
        TEST_ASSERT(duration.count() >= 900 && duration.count() <= 2000, "Timeout should be approximately 1 second");
        TestLogger::Log("Connection attempt took " + std::to_string(duration.count()) + " ms");

        P2pClose(socket);
    }

    // Test 2: Different timeout values
    TestLogger::Log("Testing different timeout values");
    int timeout_values[] = {500, 2000, 5000};
    const char* timeout_names[] = {"500ms", "2s", "5s"};

    for (int i = 0; i < 3; i++) {
        socket = P2pCreate(&options);
        if (socket) {
            TestLogger::Log("Testing timeout: " + std::string(timeout_names[i]));

            int timeout_result = P2pSetConnTimeout(socket, timeout_values[i]);
            TEST_ASSERT(timeout_result == 0, "Set timeout " + std::string(timeout_names[i]));

            auto start_time = std::chrono::high_resolution_clock::now();
            int connect_result = P2pConnect(socket, "*********", 12346);
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

            TEST_ASSERT(connect_result != 0, "Connection should fail for " + std::string(timeout_names[i]));
            TestLogger::Log("Timeout " + std::string(timeout_names[i]) + " took " + std::to_string(duration.count()) + " ms");

            P2pClose(socket);
        }
        SleepMs(100); // Small delay between tests
    }

    // Test 3: Invalid timeout values
    TestLogger::Log("Testing invalid timeout values");
    socket = P2pCreate(&options);
    if (socket) {
        // Test negative timeout
        int result = P2pSetConnTimeout(socket, -1);
        TEST_ASSERT(result != 0, "Negative timeout should be rejected");

        // Test zero timeout
        result = P2pSetConnTimeout(socket, 0);
        // Zero timeout behavior is implementation-dependent, just log the result
        TestLogger::Log("Zero timeout result: " + std::to_string(result));

        // Test very large timeout
        result = P2pSetConnTimeout(socket, 3600000); // 1 hour
        TEST_ASSERT(result == 0, "Large timeout should be accepted");

        P2pClose(socket);
    }

    Cert_Destroy(cert_handle);
    TestLogger::Log("Connection timeout test completed");
}

// Test edge cases and boundary conditions
void TestEdgeCases() {
    TestLogger::Log("=== Testing Edge Cases ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for edge cases test");
        return;
    }

    // Test with invalid socket options
    SocketOptions invalid_options = {};
    invalid_options.mode = (enum SOCKET_MODE)999; // Invalid mode
    invalid_options.type = (enum SOCKET_TYPE)999; // Invalid type

    P2P_SOCKET socket = P2pCreate(&invalid_options);
    TEST_ASSERT(socket == nullptr, "Create socket with invalid options");

    // Test with zero worker number
    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    options.wokernum = 0;

    socket = P2pCreate(&options);
    // This might succeed or fail depending on implementation
    if (socket) {
        P2pClose(socket);
    }
    TEST_ASSERT(true, "Create socket with zero workers (implementation dependent)");

    // Test binding to invalid port - P2pBind only saves parameters, validation happens in P2pListen
    options = CreateSocketOptions(MODE_SERVER, cert_handle);
    socket = P2pCreate(&options);

    if (socket) {
        // P2pBind should succeed (only saves parameters), but P2pListen should fail
        int bind_result = P2pBind(socket, "0.0.0.0", -1); // Invalid port
        TEST_ASSERT(bind_result != 0, "P2pBind should succeed (only saves parameters)");

        int listen_result = P2pListen(socket); // This should fail due to invalid port
        TEST_ASSERT(listen_result != 0, "P2pListen should fail with invalid port");

        P2pClose(socket);
    }

    // Test port out of range
    socket = P2pCreate(&options);
    if (socket) {
        int bind_result = P2pBind(socket, "0.0.0.0", 65536); // Port out of range
        TEST_ASSERT(bind_result != 0, "P2pBind should succeed (only saves parameters)");

        int listen_result = P2pListen(socket); // This should fail due to invalid port
        TEST_ASSERT(listen_result != 0, "P2pListen should fail with port out of range");

        P2pClose(socket);
    }

    // Test invalid IP address
    socket = P2pCreate(&options);
    if (socket) {
        int bind_result = P2pBind(socket, "invalid.ip.address", 4433); // Invalid IP
        TEST_ASSERT(bind_result != 0, "P2pBind should succeed (only saves parameters)");

        int listen_result = P2pListen(socket); // This should fail due to invalid IP
        TEST_ASSERT(listen_result != 0, "P2pListen should fail with invalid IP address");

        P2pClose(socket);
    }

    Cert_Destroy(cert_handle);
}

// Test data size boundaries with different data sizes
void TestDataSizeBoundaries() {
    TestLogger::Log("=== Testing Data Size Boundaries ===");

    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for data size test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    int test_port = g_config.test_port + 60; // Use different port to avoid conflicts

    // Test different data sizes
    struct SizeTest {
        int size;
        const char* description;
        bool should_test; // Some large tests might be skipped in quick runs
    };

    SizeTest size_tests[] = {
        {0, "Zero bytes", true},
        {1, "Single byte", true},
        {16, "16 bytes", true},
        {1024, "1KB", true},
        {4096, "4KB", true},
        {64*1024, "64KB", true},
        {256*1024, "256KB", true},
        {1024*1024, "1MB", false} // Large test, might be slow
    };

    int num_tests = sizeof(size_tests) / sizeof(size_tests[0]);

    for (int test_idx = 0; test_idx < num_tests; test_idx++) {
        SizeTest& test = size_tests[test_idx];

        if (!test.should_test) {
            TestLogger::Log("Skipping large test: " + std::string(test.description));
            continue;
        }

        TestLogger::Log("Testing data size: " + std::string(test.description));

        // Reset global state
        g_server_running = false;
        g_client_connected = false;
        g_test_completed = false;

        // Create test data
        char* test_data = nullptr;
        if (test.size > 0) {
            test_data = new char[test.size];
            // Fill with pattern data for verification
            for (int i = 0; i < test.size; i++) {
                test_data[i] = (char)(i % 256);
            }
        }

        // Start server thread for this size test
        std::thread server_thread([server_cert, test_port, test_idx, &test, test_data]() {
            TestLogger::Log("Size test server starting for: " + std::string(test.description));

            SocketOptions server_options = CreateSocketOptions(MODE_SERVER, server_cert);
            P2P_SOCKET server_socket = P2pCreate(&server_options);

            if (!server_socket) {
                TestLogger::Log(LOG_ERROR, "Failed to create size test server socket");
                return;
            }

            // Bind and listen - P2pBind only saves parameters, should always succeed
            P2pBind(server_socket, "0.0.0.0", test_port + test_idx); // Should always succeed

            if (P2pListen(server_socket) != 0) {
                TestLogger::Log(LOG_ERROR, "Failed to listen on size test server socket");
                P2pClose(server_socket);
                return;
            }

            g_server_running = true;
            TestLogger::Log("Size test server listening on port " + std::to_string(test_port + test_idx));

            // Accept client connection
            char client_ip[128] = {0};
            int client_port = 0;

            P2P_SOCKET client_socket = P2pAccept(server_socket, client_ip, sizeof(client_ip), &client_port);

            if (client_socket) {
                TestLogger::Log("Size test server accepted connection for: " + std::string(test.description));

                if (test.size > 0) {
                    // Read the test data
                    char* recv_buffer = new char[test.size];
                    int bytes_read = P2pRead(client_socket, recv_buffer, test.size);

                    if (bytes_read == test.size) {
                        // Verify data integrity
                        bool data_correct = true;
                        for (int i = 0; i < test.size; i++) {
                            if (recv_buffer[i] != test_data[i]) {
                                data_correct = false;
                                break;
                            }
                        }

                        if (data_correct) {
                            TestLogger::Log("Size test server: data integrity verified for " + std::string(test.description));
                            // Echo back the data
                            int bytes_written = P2pWrite(client_socket, recv_buffer, test.size);
                            TestLogger::Log("Size test server echoed " + std::to_string(bytes_written) + " bytes");
                        } else {
                            TestLogger::Log(LOG_ERROR, "Size test server: data corruption detected for " + std::string(test.description));
                        }
                    } else {
                        TestLogger::Log(LOG_ERROR, "Size test server: read " + std::to_string(bytes_read) + " bytes, expected " + std::to_string(test.size));
                    }

                    delete[] recv_buffer;
                } else {
                    // Handle zero-byte case
                    TestLogger::Log("Size test server: handling zero-byte test");
                    char dummy;
                    int bytes_read = P2pRead(client_socket, &dummy, 0);
                    TestLogger::Log("Zero-byte read result: " + std::to_string(bytes_read));

                    // Echo back zero bytes
                    int bytes_written = P2pWrite(client_socket, nullptr, 0);
                    TestLogger::Log("Zero-byte write result: " + std::to_string(bytes_written));
                }

                P2pClose(client_socket);
            }

            P2pClose(server_socket);
            g_server_running = false;
            TestLogger::Log("Size test server thread exiting for: " + std::string(test.description));
        });

        // Wait for server to start
        int wait_count = 0;
        while (!g_server_running && wait_count < 50) {
            SleepMs(100);
            wait_count++;
        }

        if (g_server_running) {
            // Create client socket and test the specific data size
            SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, client_cert);
            P2P_SOCKET client_socket = P2pCreate(&client_options);

            if (client_socket) {
                P2pSetConnTimeout(client_socket, 5000);

                // Connect to server
                int connect_result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port + test_idx);
                TEST_ASSERT(connect_result == 0, "Client connect for " + std::string(test.description));

                if (connect_result == 0) {
                    TestLogger::Log("Size test client connected for: " + std::string(test.description));

                    auto start_time = std::chrono::high_resolution_clock::now();

                    // Send test data
                    int bytes_written;
                    if (test.size > 0) {
                        bytes_written = P2pWrite(client_socket, test_data, test.size);
                    } else {
                        bytes_written = P2pWrite(client_socket, nullptr, 0);
                    }

                    auto write_time = std::chrono::high_resolution_clock::now();
                    auto write_duration = std::chrono::duration_cast<std::chrono::microseconds>(write_time - start_time);

                    TEST_ASSERT(bytes_written == test.size, "Client write for " + std::string(test.description));
                    TestLogger::Log("Write " + std::string(test.description) + " completed in " + std::to_string(write_duration.count()) + " microseconds");

                    if (bytes_written == test.size) {
                        // Read echo response
                        auto read_start = std::chrono::high_resolution_clock::now();

                        int bytes_read;
                        if (test.size > 0) {
                            char* read_buffer = new char[test.size];
                            bytes_read = P2pRead(client_socket, read_buffer, test.size);

                            if (bytes_read == test.size) {
                                // Verify echoed data
                                bool echo_correct = true;
                                for (int i = 0; i < test.size; i++) {
                                    if (read_buffer[i] != test_data[i]) {
                                        echo_correct = false;
                                        break;
                                    }
                                }
                                TEST_ASSERT(echo_correct, "Echo data integrity for " + std::string(test.description));
                            }

                            delete[] read_buffer;
                        } else {
                            char dummy;
                            bytes_read = P2pRead(client_socket, &dummy, 0);
                        }

                        auto read_end = std::chrono::high_resolution_clock::now();
                        auto read_duration = std::chrono::duration_cast<std::chrono::microseconds>(read_end - read_start);

                        TEST_ASSERT(bytes_read == test.size, "Client read for " + std::string(test.description));
                        TestLogger::Log("Read " + std::string(test.description) + " completed in " + std::to_string(read_duration.count()) + " microseconds");

                        // Calculate throughput for larger sizes
                        if (test.size >= 1024) {
                            double total_time_ms = (write_duration.count() + read_duration.count()) / 1000.0;
                            double throughput_mbps = (test.size * 2 * 8.0) / (total_time_ms * 1000.0); // Mbps
                            TestLogger::Log("Throughput for " + std::string(test.description) + ": " + std::to_string(throughput_mbps) + " Mbps");
                        }
                    }
                }

                P2pClose(client_socket);
            }
        }

        // Signal test completion and wait for server thread
        g_test_completed = true;
        if (server_thread.joinable()) {
            server_thread.join();
        }

        // Clean up test data
        if (test_data) {
            delete[] test_data;
        }

        SleepMs(100); // Small delay between size tests
    }

    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);
    TestLogger::Log("Data size boundaries test completed");
}

// Test advanced P2pWritev scenarios
void TestWritevAdvanced() {
    TestLogger::Log("=== Testing Advanced P2pWritev Scenarios ===");

    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for advanced writev test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    int test_port = g_config.test_port + 70; // Use different port to avoid conflicts

    // Test different iovec scenarios
    struct WritevTest {
        const char* description;
        int iov_count;
        bool include_zero_length;
        bool include_large_buffer;
    };

    WritevTest writev_tests[] = {
        {"Single iovec", 1, false, false},
        {"Multiple small iovecs", 5, false, false},
        {"Iovecs with zero-length", 3, true, false},
        {"Large number of iovecs", 10, false, false},
        {"Mixed size iovecs", 4, false, true}
    };

    int num_tests = sizeof(writev_tests) / sizeof(writev_tests[0]);

    for (int test_idx = 0; test_idx < num_tests; test_idx++) {
        WritevTest& test = writev_tests[test_idx];
        TestLogger::Log("Testing writev scenario: " + std::string(test.description));

        // Reset global state
        g_server_running = false;
        g_client_connected = false;
        g_test_completed = false;

        // Prepare test data and iovec structures
        std::vector<std::string> data_buffers;
        std::vector<struct p2p_iovec> iov_array;
        int total_expected_size = 0;

        // Create test data based on scenario
        for (int i = 0; i < test.iov_count; i++) {
            std::string data;

            if (test.include_zero_length && i == 1) {
                // Include a zero-length buffer
                data = "";
            } else if (test.include_large_buffer && i == test.iov_count - 1) {
                // Last buffer is large
                data = std::string(1024, 'A' + i);
            } else {
                // Regular small buffers
                data = "Buffer" + std::to_string(i) + "_";
            }

            data_buffers.push_back(data);

            struct p2p_iovec iov;
            iov.iov_base = (void*)data_buffers[i].c_str();
            iov.iov_len = data_buffers[i].length();
            iov_array.push_back(iov);

            total_expected_size += data_buffers[i].length();
        }

        // Create expected combined data for verification
        std::string expected_combined;
        for (const auto& data : data_buffers) {
            expected_combined += data;
        }

        // Start server thread for this writev test
        std::thread server_thread([server_cert, test_port, test_idx, &test, total_expected_size, expected_combined]() {
            TestLogger::Log("Writev test server starting for: " + std::string(test.description));

            SocketOptions server_options = CreateSocketOptions(MODE_SERVER, server_cert);
            P2P_SOCKET server_socket = P2pCreate(&server_options);

            if (!server_socket) {
                TestLogger::Log(LOG_ERROR, "Failed to create writev test server socket");
                return;
            }

            // Bind and listen - P2pBind only saves parameters, should always succeed
            P2pBind(server_socket, "0.0.0.0", test_port + test_idx); // Should always succeed

            if (P2pListen(server_socket) != 0) {
                TestLogger::Log(LOG_ERROR, "Failed to listen on writev test server socket");
                P2pClose(server_socket);
                return;
            }

            g_server_running = true;
            TestLogger::Log("Writev test server listening on port " + std::to_string(test_port + test_idx));

            // Accept client connection
            char client_ip[128] = {0};
            int client_port = 0;

            P2P_SOCKET client_socket = P2pAccept(server_socket, client_ip, sizeof(client_ip), &client_port);

            if (client_socket) {
                TestLogger::Log("Writev test server accepted connection for: " + std::string(test.description));

                if (total_expected_size > 0) {
                    // Read the combined data
                    char* recv_buffer = new char[total_expected_size + 1];
                    int bytes_read = P2pRead(client_socket, recv_buffer, total_expected_size);

                    if (bytes_read == total_expected_size) {
                        recv_buffer[bytes_read] = '\0';
                        std::string received_data(recv_buffer, bytes_read);

                        if (received_data == expected_combined) {
                            TestLogger::Log("Writev test server: data integrity verified for " + std::string(test.description));
                            // Echo back the data
                            int bytes_written = P2pWrite(client_socket, recv_buffer, bytes_read);
                            TestLogger::Log("Writev test server echoed " + std::to_string(bytes_written) + " bytes");
                        } else {
                            TestLogger::Log(LOG_ERROR, "Writev test server: data mismatch for " + std::string(test.description));
                            TestLogger::Log("Expected: " + expected_combined);
                            TestLogger::Log("Received: " + received_data);
                        }
                    } else {
                        TestLogger::Log(LOG_ERROR, "Writev test server: read " + std::to_string(bytes_read) + " bytes, expected " + std::to_string(total_expected_size));
                    }

                    delete[] recv_buffer;
                } else {
                    // Handle zero-size case
                    TestLogger::Log("Writev test server: handling zero-size test");
                    char dummy;
                    int bytes_read = P2pRead(client_socket, &dummy, 0);
                    TestLogger::Log("Zero-size read result: " + std::to_string(bytes_read));

                    int bytes_written = P2pWrite(client_socket, nullptr, 0);
                    TestLogger::Log("Zero-size write result: " + std::to_string(bytes_written));
                }

                P2pClose(client_socket);
            }

            P2pClose(server_socket);
            g_server_running = false;
            TestLogger::Log("Writev test server thread exiting for: " + std::string(test.description));
        });

        // Wait for server to start
        int wait_count = 0;
        while (!g_server_running && wait_count < 50) {
            SleepMs(100);
            wait_count++;
        }

        if (g_server_running) {
            // Create client socket and test the specific writev scenario
            SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, client_cert);
            P2P_SOCKET client_socket = P2pCreate(&client_options);

            if (client_socket) {
                P2pSetConnTimeout(client_socket, 5000);

                // Connect to server
                int connect_result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port + test_idx);
                TEST_ASSERT(connect_result == 0, "Client connect for " + std::string(test.description));

                if (connect_result == 0) {
                    TestLogger::Log("Writev test client connected for: " + std::string(test.description));

                    auto start_time = std::chrono::high_resolution_clock::now();

                    // Perform writev operation
                    int bytes_written = P2pWritev(client_socket, iov_array.data(), iov_array.size());

                    auto write_time = std::chrono::high_resolution_clock::now();
                    auto write_duration = std::chrono::duration_cast<std::chrono::microseconds>(write_time - start_time);

                    TEST_ASSERT(bytes_written == total_expected_size, "Writev bytes written for " + std::string(test.description));
                    TestLogger::Log("Writev " + std::string(test.description) + " wrote " + std::to_string(bytes_written) + " bytes in " + std::to_string(write_duration.count()) + " microseconds");

                    if (bytes_written == total_expected_size) {
                        // Read echo response
                        auto read_start = std::chrono::high_resolution_clock::now();

                        int bytes_read;
                        if (total_expected_size > 0) {
                            char* read_buffer = new char[total_expected_size + 1];
                            bytes_read = P2pRead(client_socket, read_buffer, total_expected_size);

                            if (bytes_read == total_expected_size) {
                                read_buffer[bytes_read] = '\0';
                                std::string received_echo(read_buffer, bytes_read);
                                TEST_ASSERT(received_echo == expected_combined, "Writev echo data integrity for " + std::string(test.description));
                            }

                            delete[] read_buffer;
                        } else {
                            char dummy;
                            bytes_read = P2pRead(client_socket, &dummy, 0);
                        }

                        auto read_end = std::chrono::high_resolution_clock::now();
                        auto read_duration = std::chrono::duration_cast<std::chrono::microseconds>(read_end - read_start);

                        TEST_ASSERT(bytes_read == total_expected_size, "Writev echo read for " + std::string(test.description));
                        TestLogger::Log("Writev echo read completed in " + std::to_string(read_duration.count()) + " microseconds");

                        TestLogger::Log("Writev test successful for: " + std::string(test.description));
                    }
                }

                P2pClose(client_socket);
            }
        }

        // Signal test completion and wait for server thread
        g_test_completed = true;
        if (server_thread.joinable()) {
            server_thread.join();
        }

        SleepMs(100); // Small delay between writev tests
    }

    // Test error conditions
    TestLogger::Log("Testing writev error conditions");
    SocketOptions options = CreateSocketOptions(MODE_CLIENT, client_cert);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test with null iovec array
        int result = P2pWritev(socket, nullptr, 1);
        TEST_ASSERT(result <= 0, "Writev with null iovec array should fail");

        // Test with zero count
        struct p2p_iovec dummy_iov = {(void*)"test", 4};
        result = P2pWritev(socket, &dummy_iov, 0);
        TEST_ASSERT(result == 0, "Writev with zero count should return 0");

        // Test with invalid iovec (null base)
        struct p2p_iovec invalid_iov = {nullptr, 10};
        result = P2pWritev(socket, &invalid_iov, 1);
        TEST_ASSERT(result <= 0, "Writev with null iov_base should fail");

        P2pClose(socket);
    }

    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);
    TestLogger::Log("Advanced writev test completed");
}

// Test multiple client connections to one server
void TestMultipleClientConnections() {
    TestLogger::Log("=== Testing Multiple Client Connections ===");

    const int NUM_CLIENTS = 3;
    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for multi-client test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    int test_port = g_config.test_port + 30; // Use different port to avoid conflicts

    // Reset global state
    g_server_running = false;
    g_client_connected = false;
    g_test_completed = false;
    g_connected_clients = 0;
    g_expected_clients = NUM_CLIENTS;

    // Clear client sockets vector
    {
        std::lock_guard<std::mutex> lock(g_client_sockets_mutex);
        g_client_sockets.clear();
    }

    TestLogger::Log("Starting multi-client test with " + std::to_string(NUM_CLIENTS) + " clients on port " + std::to_string(test_port));

    // Start server thread
    std::thread server_thread([server_cert, test_port]() {
        MultiClientServerFunction(server_cert, test_port);
    });

    // Wait for server to start
    int wait_count = 0;
    while (!g_server_running && wait_count < 50) { // Wait up to 5 seconds
        SleepMs(100);
        wait_count++;
    }

    TEST_ASSERT(g_server_running, "Multi-client server started successfully");

    if (g_server_running) {
        // Create multiple client threads
        std::vector<std::thread> client_threads;
        // Use array instead of vector<bool> to avoid proxy reference issues
        bool client_results[NUM_CLIENTS];
        for (int i = 0; i < NUM_CLIENTS; i++) {
            client_results[i] = false;
        }

        // Start all client threads with a small delay between them
        for (int i = 0; i < NUM_CLIENTS; i++) {
            client_threads.emplace_back([client_cert, test_port, i, &client_results]() {
                ClientConnectionFunction(client_cert, test_port, i, &client_results[i]);
            });
            SleepMs(200); // Small delay to stagger connections
        }

        // Wait for all clients to complete
        for (auto& client_thread : client_threads) {
            if (client_thread.joinable()) {
                client_thread.join();
            }
        }

        // Verify results
        int successful_clients = 0;
        for (int i = 0; i < NUM_CLIENTS; i++) {
            if (client_results[i]) {
                successful_clients++;
            }
            TEST_ASSERT(client_results[i], "Client " + std::to_string(i) + " connection and data exchange");
        }

        TestLogger::Log("Multi-client test completed: " + std::to_string(successful_clients) + "/" +
                       std::to_string(NUM_CLIENTS) + " clients successful");

        // Test overall success
        TEST_ASSERT(successful_clients == NUM_CLIENTS, "All clients connected and exchanged data successfully");

        // Verify server accepted the expected number of clients
        TEST_ASSERT(g_connected_clients.load() == NUM_CLIENTS, "Server accepted expected number of clients");
    }

    // Signal test completion and wait for server thread
    g_test_completed = true;
    if (server_thread.joinable()) {
        server_thread.join();
    }

    // Clean up any remaining client sockets
    {
        std::lock_guard<std::mutex> lock(g_client_sockets_mutex);
        for (auto socket : g_client_sockets) {
            if (socket) {
                P2pClose(socket);
            }
        }
        g_client_sockets.clear();
    }

    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);

    TestLogger::Log("Multi-client test cleanup completed");
}

int main(int argc, char* argv[]) {
#ifndef _WIN32
    signal(SIGPIPE, SIG_IGN);
#endif

    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "-type" && i + 1 < argc) {
            g_config.socket_type = (enum SOCKET_TYPE)std::stoi(argv[++i]);
        } else if (arg == "-port" && i + 1 < argc) {
            g_config.test_port = std::stoi(argv[++i]);
        } else if (arg == "-ip" && i + 1 < argc) {
            g_config.test_ip = argv[++i];
        } else if (arg == "-workers" && i + 1 < argc) {
            g_config.worker_num = std::stoi(argv[++i]);
        } else if (arg == "-test" && i + 1 < argc) {
            // Disable all tests first
            g_config.run_basic_ops = false;
            g_config.run_invalid_params = false;
            g_config.run_binding = false;
            g_config.run_settings = false;
            g_config.run_polling = false;
            g_config.run_connection = false;
            g_config.run_vectored_io = false;
            g_config.run_multi_client = false;
            g_config.run_edge_cases = false;

            // Disable additional comprehensive tests
            g_config.run_get_local_port = false;
            g_config.run_connection_timeout = false;
            g_config.run_data_size_boundaries = false;
            g_config.run_writev_advanced = false;

            // Enable specific test
            std::string test_name = argv[++i];
            if (test_name == "basic") g_config.run_basic_ops = true;
            else if (test_name == "invalid") g_config.run_invalid_params = true;
            else if (test_name == "binding") g_config.run_binding = true;
            else if (test_name == "settings") g_config.run_settings = true;
            else if (test_name == "polling") g_config.run_polling = true;
            else if (test_name == "connection") g_config.run_connection = true;
            else if (test_name == "vectored") g_config.run_vectored_io = true;
            else if (test_name == "multiclient") g_config.run_multi_client = true;
            else if (test_name == "edge") g_config.run_edge_cases = true;
            // Additional comprehensive tests
            else if (test_name == "getport") g_config.run_get_local_port = true;
            else if (test_name == "timeout") g_config.run_connection_timeout = true;
            else if (test_name == "datasize") g_config.run_data_size_boundaries = true;
            else if (test_name == "writevadv") g_config.run_writev_advanced = true;
            else if (test_name == "all") {
                g_config.run_basic_ops = true;
                g_config.run_invalid_params = true;
                g_config.run_binding = true;
                g_config.run_settings = true;
                g_config.run_polling = true;
                g_config.run_connection = true;
                g_config.run_vectored_io = true;
                g_config.run_multi_client = true;
                g_config.run_edge_cases = true;
                // Enable additional comprehensive tests
                g_config.run_get_local_port = true;
                g_config.run_connection_timeout = true;
                g_config.run_data_size_boundaries = true;
                g_config.run_writev_advanced = true;
            } else {
                std::cout << "Unknown test: " << test_name << std::endl;
                return 1;
            }
        } else if (arg == "-h" || arg == "--help") {
            std::cout << "Usage: " << argv[0] << " [options]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  -type <n>     Socket type (1=MULTITCP, 2=SSL, 3=QUIC)" << std::endl;
            std::cout << "  -port <n>     Test port (default: 4433)" << std::endl;
            std::cout << "  -ip <addr>    Test IP address (default: 127.0.0.1)" << std::endl;
            std::cout << "  -workers <n>  Number of workers (default: 4)" << std::endl;
            std::cout << "  -test <name>  Run specific test:" << std::endl;
            std::cout << "                basic      - Basic socket operations" << std::endl;
            std::cout << "                invalid    - Invalid parameter tests" << std::endl;
            std::cout << "                binding    - Socket binding tests" << std::endl;
            std::cout << "                settings   - Socket settings tests" << std::endl;
            std::cout << "                polling    - Polling operations tests" << std::endl;
            std::cout << "                connection - Server-client connection tests" << std::endl;
            std::cout << "                vectored   - Vectored I/O tests" << std::endl;
            std::cout << "                multiclient- Multi-client tests" << std::endl;
            std::cout << "                edge       - Edge cases tests" << std::endl;
            std::cout << "                getport    - P2pGetLocalPort comprehensive tests" << std::endl;
            std::cout << "                timeout    - Connection timeout tests" << std::endl;
            std::cout << "                datasize   - Data size boundary tests" << std::endl;
            std::cout << "                writevadv  - Advanced P2pWritev tests" << std::endl;
            std::cout << "                all        - Run all tests (default)" << std::endl;
            std::cout << "  -h, --help    Show this help message" << std::endl;
            return 0;
        }
    }

    TestLogger::Log("Starting P2P Socket Interface Unit Tests (Basic API Only)");
    TestLogger::Log("Configuration:");
    TestLogger::Log("  Socket type: " + std::to_string(g_config.socket_type));
    TestLogger::Log("  Test port: " + std::to_string(g_config.test_port));
    TestLogger::Log("  Test IP: " + g_config.test_ip);
    TestLogger::Log("  Workers: " + std::to_string(g_config.worker_num));
    TestLogger::Log("");

    // Run tests based on configuration
    if (g_config.run_basic_ops) {
        TestBasicSocketOperations();
    }

    if (g_config.run_invalid_params) {
        TestInvalidParameters();
    }

    if (g_config.run_binding) {
        TestSocketBinding();
    }

    if (g_config.run_settings) {
        TestSocketSettings();
    }

    if (g_config.run_polling) {
        TestPollingOperations();
    }

    if (g_config.run_connection) {
        TestServerClientConnection();
    }

    if (g_config.run_vectored_io) {
        TestVectoredIO();
    }

    if (g_config.run_multi_client) {
        TestMultipleClientConnections();
    }

    if (g_config.run_edge_cases) {
        TestEdgeCases();
    }

    // Run additional comprehensive tests
    bool run_any_additional = g_config.run_get_local_port ||
                             g_config.run_connection_timeout ||
                             g_config.run_data_size_boundaries ||
                             g_config.run_writev_advanced;

    if (run_any_additional) {
        TestLogger::Log("=== Running Additional Comprehensive Tests ===");
    }

    if (g_config.run_get_local_port) {
        TestGetLocalPort();
    }

    if (g_config.run_connection_timeout) {
        TestConnectionTimeout();
    }

    if (g_config.run_data_size_boundaries) {
        TestDataSizeBoundaries();
    }

    if (g_config.run_writev_advanced) {
        TestWritevAdvanced();
    }

    // Print test summary
    TestLogger::Log("");
    g_testResult.PrintSummary();

    if (g_testResult.failed_tests == 0) {
        TestLogger::Log("All tests passed!");
    } else {
        TestLogger::Log("Some tests failed!");
    }

    TestLogger::Log("Unit tests completed");
    return g_testResult.failed_tests > 0 ? 1 : 0;
}
