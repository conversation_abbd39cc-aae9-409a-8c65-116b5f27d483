// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.

/**
 * @file p2psocket.h
 * @brief P2P Socket Interface - High-level socket API supporting multiple transport protocols
 *
 * This header provides a unified socket interface that supports multiple transport protocols
 * including TCP, SSL/TLS, and QUIC. The API is designed to be protocol-agnostic, allowing
 * applications to switch between different transport protocols without changing the code.
 *
 * Key Features:
 * - Protocol abstraction (TCP, SSL, QUIC)
 * - Synchronous and asynchronous I/O operations
 * - Vectored I/O support
 * - Connection management and polling
 * - Configurable send/receive modes
 * - Certificate-based authentication
 */

#pragma once
#include "common_defines.h"

// Platform-specific API export/import definitions
#ifdef FS_STATIC_LIB
#define PLUGIN_API
#else
#if defined(WIN32) || defined(_WIN32)
#ifdef DLL_EXPORT
#define PLUGIN_API __declspec(dllexport)
#else
#define PLUGIN_API __declspec(dllimport)
#endif
#endif

#if defined(ANDROID) || defined(__linux__)
#define PLUGIN_API __attribute__((visibility("default")))
#endif
#endif

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// Core Socket Management API
// ============================================================================

/**
 * @brief Create a new P2P socket with specified options
 * @param option Pointer to SocketOptions structure containing socket configuration
 * @return P2P_SOCKET handle on success, NULL on failure
 *
 * Creates a new socket instance with the specified transport protocol, mode, and
 * certificate configuration. The socket must be properly configured before use.
 */
PLUGIN_API P2P_SOCKET P2pCreate(SocketOptions* option);

/**
 * @brief Poll socket for I/O events with timeout
 * @param soc Socket handle to poll
 * @param events Pointer to PollEvent structure for event specification and results
 * @param timeout Timeout in milliseconds (0 = non-blocking, -1 = infinite)
 * @return Number of events detected (>0), 0 on timeout, <0 on error
 *
 * Monitors the socket for specified events (readable, writable, error conditions).
 * This function is essential for non-blocking I/O operations and event-driven programming.
 */
PLUGIN_API int P2pPoll(P2P_SOCKET soc, PollEvent* events, int timeout);

/**
 * @brief Set connection timeout for the socket
 * @param soc Socket handle
 * @param timeout Connection timeout in milliseconds
 * @return 0 on success, non-zero on error
 *
 * Configures the maximum time to wait for connection establishment.
 * This setting affects P2pConnect() behavior.
 */
PLUGIN_API int P2pSetConnTimeout(P2P_SOCKET soc, int timeout);

/**
 * @brief Bind socket to a local address and port, this api is different from socket api,for p2pbind only save port ,and check logic is in p2plistern.
 * @param soc Socket handle
 * @param ipaddr Local IP address to bind to ("0.0.0.0" for all interfaces)
 * @param port Local port number to bind to
 * @return 0 on success, non-zero on error
 *
 * Associates the socket with a local network address. Required for server sockets
 * before calling P2pListen(). For client sockets, binding is optional.
 */
PLUGIN_API int P2pBind(P2P_SOCKET soc, const char* ipaddr, int port);

/**
 * @brief Connect to a remote peer
 * @param soc Socket handle
 * @param ipaddr Remote IP address to connect to
 * @param port Remote port number to connect to
 * @return 0 on success, non-zero on error
 *
 * Establishes a connection to the specified remote address. This is a blocking
 * operation that respects the connection timeout set by P2pSetConnTimeout().
 * For QUIC protocol, this includes the handshake process.
 */
PLUGIN_API int P2pConnect(P2P_SOCKET soc, const char* ipaddr, int port);

/**
 * @brief Write data to the socket
 * @param soc Socket handle
 * @param buffer Pointer to data buffer to send
 * @param len Number of bytes to send
 * @return Number of bytes actually sent on success, <=0 on error
 *
 * Sends data through the socket. The behavior depends on the send mode:
 * - Direct mode: Data is sent immediately through the transport layer
 * - Buffer mode: Data may be buffered and sent asynchronously
 */
PLUGIN_API int P2pWrite(P2P_SOCKET soc, const char* buffer, int len);

/**
 * @brief Write data using vectored I/O (scatter-gather)
 * @param soc Socket handle
 * @param iov Array of p2p_iovec structures containing buffer pointers and lengths
 * @param count Number of iovec structures in the array
 * @return Total number of bytes sent on success, <=0 on error
 *
 * Performs vectored write operation, sending data from multiple buffers in a single call.
 * This is more efficient than multiple P2pWrite() calls when sending fragmented data.
 * The data from all buffers is sent as a continuous stream.
 */
PLUGIN_API int P2pWritev(P2P_SOCKET soc, struct p2p_iovec* iov, int count);

/**
 * @brief Read data from the socket
 * @param soc Socket handle
 * @param buffer Pointer to buffer where received data will be stored
 * @param len Number of bytes to read
 * @return Number of bytes actually read on success, 0 on connection close, <0 on error
 *
 * Reads data from the socket. IMPORTANT: If len is larger than available data,
 * this function will BLOCK and wait until all requested data is available.
 * The buffer must be large enough to hold the requested data.
 *
 * Behavior:
 * - Blocks until exactly 'len' bytes are received or connection is closed
 * - Returns the actual number of bytes read (may be less than 'len' on connection close)
 * - Returns 0 when peer closes the connection gracefully
 * - Returns <0 on error conditions
 */
PLUGIN_API int P2pRead(P2P_SOCKET soc, char* buffer, int len);

// ============================================================================
// Socket Configuration API
// ============================================================================

/**
 * @brief Set socket send mode
 * @param soc Socket handle
 * @param directMode Send mode: 1 = direct mode, 0 = buffer mode
 * @return 0 on success, non-zero on error
 *
 * Configures how data is sent through the socket:
 * - Direct mode (1): Data is sent immediately through the transport layer
 * - Buffer mode (0): Data may be buffered and sent asynchronously for better performance
 */
PLUGIN_API int P2pSetSendMode(P2P_SOCKET soc, int directMode);

/**
 * @brief Set socket receive mode
 * @param soc Socket handle
 * @param directMode Receive mode: 1 = direct mode, 0 = buffer mode
 * @return 0 on success, non-zero on error
 *
 * Configures how data is received from the socket:
 * - Direct mode (1): Data is read directly from the transport layer
 * - Buffer mode (0): Data is buffered internally for improved performance
 */
PLUGIN_API int P2pSetReadMode(P2P_SOCKET soc, int directMode);

// ============================================================================
// Server Socket API
// ============================================================================

/**
 * @brief Accept incoming connection on a listening socket
 * @param soc Listening socket handle
 * @param ipaddr Buffer to store client IP address (output parameter)
 * @param ipaddr_len Size of the ipaddr buffer
 * @param port Pointer to store client port number (output parameter)
 * @return New socket handle for the accepted connection, NULL on error
 *
 * Accepts a pending connection on a listening socket. This is a blocking operation
 * that waits for an incoming connection. The returned socket represents the
 * established connection with the client.
 */
PLUGIN_API P2P_SOCKET P2pAccept(P2P_SOCKET soc,
                                char* ipaddr,
                                int ipaddr_len,
                                int* port);

/**
 * @brief Put socket into listening state for incoming connections
 * @param soc Socket handle (must be bound to an address)
 * @return 0 on success, non-zero on error
 *
 * Configures the socket to accept incoming connections. The socket must be
 * bound to a local address using P2pBind() before calling this function.
 * After successful listen, use P2pAccept() to accept incoming connections.
 */
PLUGIN_API int P2pListen(P2P_SOCKET soc);

/**
 * @brief Get the local port number of the socket
 * @param soc Socket handle
 * @return Local port number on success, <0 on error
 *
 * Retrieves the local port number that the socket is bound to.
 * Useful for determining the actual port when binding to port 0 (auto-assign).
 */
PLUGIN_API int P2pGetLocalPort(P2P_SOCKET soc);

/**
 * @brief Close the socket and release resources
 * @param soc Socket handle to close
 * @return 0 on success, non-zero on error
 *
 * Closes the socket connection and releases all associated resources.
 * After calling this function, the socket handle becomes invalid and
 * should not be used in any further operations.
 */
PLUGIN_API int P2pClose(P2P_SOCKET soc);

// Stream management API
PLUGIN_API P2P_STREAM P2pStreamCreate(P2P_SOCKET soc, struct StreamOptions* options);
PLUGIN_API int P2pStreamClose(P2P_STREAM stream);

// Stream data transmission API
PLUGIN_API int P2pStreamWrite(P2P_STREAM stream, const char* buffer, int len);
PLUGIN_API int P2pStreamWritev(P2P_STREAM stream, struct p2p_iovec* iov, int count);
PLUGIN_API int P2pStreamRead(P2P_STREAM stream, char* buffer, int len);

// Stream state and control API
PLUGIN_API int P2pStreamPoll(P2P_STREAM stream, struct StreamEvent* events, int max_events, int timeout);
PLUGIN_API int P2pStreamGetState(P2P_STREAM stream);
PLUGIN_API int P2pStreamSetCallback(P2P_STREAM stream, StreamEventCallback callback, void* user_data);

// Stream information query API
PLUGIN_API int P2pStreamGetId(P2P_STREAM stream);
PLUGIN_API int P2pStreamGetBufferedBytes(P2P_STREAM stream);
PLUGIN_API P2P_SOCKET P2pStreamGetSocket(P2P_STREAM stream);

#ifdef __cplusplus
}
#endif
